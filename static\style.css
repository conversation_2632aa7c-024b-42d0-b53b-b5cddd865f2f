/* 游戏攻略智能助手样式 */

body, html {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container-fluid {
    height: 100vh;
    padding: 0;
}

/* 侧边栏样式 */
.sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0;
    height: 100vh;
    overflow-y: auto;
}

.sidebar-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.sidebar-header h4 {
    margin: 0;
    font-weight: 600;
}

.sidebar-content {
    padding: 20px;
}

.status-panel, .action-panel, .info-panel {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-panel h6, .info-panel h6 {
    color: #333;
    font-weight: 600;
    margin-bottom: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
    background-color: #dc3545;
    transition: background-color 0.3s;
}

.status-dot.ready {
    background-color: #28a745;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    padding: 5px 0;
    color: #666;
    font-size: 0.9em;
}

.info-list li:before {
    content: "💡 ";
    margin-right: 5px;
}

/* 聊天区域样式 */
.chat-area {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.chat-header h5 {
    margin: 0;
    font-weight: 600;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.5);
}

.message {
    display: flex;
    margin-bottom: 20px;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2em;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    margin-right: 0;
    margin-left: 15px;
}

.message-content {
    background: white;
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 70%;
    word-wrap: break-word;
}

.user-message .message-content {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.message-content p {
    margin: 0;
    line-height: 1.5;
}

.message-content p:not(:last-child) {
    margin-bottom: 10px;
}

/* 输入区域样式 */
.chat-input {
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.chat-input .form-control {
    border-radius: 25px;
    border: 2px solid #e9ecef;
    padding: 12px 20px;
    font-size: 1em;
}

.chat-input .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.chat-input .btn {
    border-radius: 50%;
    width: 50px;
    height: 50px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: transform 0.2s;
}

.chat-input .btn:hover {
    transform: scale(1.05);
}

.chat-input .btn:disabled {
    background: #6c757d;
    transform: none;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-content p {
    margin: 0;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        width: 80%;
        z-index: 1000;
        transition: left 0.3s;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .chat-area {
        width: 100%;
    }
    
    .message-content {
        max-width: 85%;
    }
}
