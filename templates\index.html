<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 金融智能助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- 侧边栏 -->
            <div class="col-md-3 sidebar">
                <div class="sidebar-header">
                    <h4><i class="fas fa-chart-line"></i> 金融智能助手</h4>
                </div>
                
                <div class="sidebar-content">
                    <div class="status-panel">
                        <h6>系统状态</h6>
                        <div id="status-indicators">
                            <div class="status-item">
                                <span class="status-dot" id="kb-status"></span>
                                <span>知识库</span>
                            </div>
                            <div class="status-item">
                                <span class="status-dot" id="agent-status"></span>
                                <span>智能体</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="action-panel">
                        <button class="btn btn-primary w-100 mb-2" id="init-btn">
                            <i class="fas fa-rocket"></i> 初始化系统
                        </button>
                        <button class="btn btn-secondary w-100 mb-2" id="clear-btn">
                            <i class="fas fa-trash"></i> 清除对话
                        </button>
                    </div>
                    
                    <div class="info-panel">
                        <h6>使用说明</h6>
                        <ul class="info-list">
                            <li>首次使用请点击"初始化系统"</li>
                            <li>支持金融投资相关问题</li>
                            <li>基于专业知识库提供建议</li>
                            <li>支持多轮对话记忆</li>
                            <li>投资有风险，建议仅供参考</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 主聊天区域 -->
            <div class="col-md-9 chat-area">
                <div class="chat-header">
                    <h5><i class="fas fa-comments"></i> 智能对话</h5>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <p>你好！我是金融智能助手 💰</p>
                            <p>请先点击左侧的"初始化系统"按钮，然后就可以向我询问金融投资相关的问题了！</p>
                            <p><small>⚠️ 提醒：所有建议仅供参考，投资有风险，请谨慎决策。</small></p>
                        </div>
                    </div>
                </div>
                
                <div class="chat-input">
                    <div class="input-group">
                        <input type="text" class="form-control" id="user-input"
                               placeholder="请输入您的金融问题..." disabled>
                        <button class="btn btn-primary" id="send-btn" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载提示 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3">正在处理中，请稍候...</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
