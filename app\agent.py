"""
游戏攻略智能体
实现RAG（检索增强生成）流程，提供智能对话功能
"""

from typing import List, Dict, Any
from loguru import logger
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
from langchain.memory import ConversationBufferWindowMemory
from app.langchain_integration import DeepSeekLLM
from app.knowledge_base import KnowledgeBase

class GameGuideAgent:
    """游戏攻略智能体"""
    
    def __init__(self, knowledge_base: KnowledgeBase):
        """
        初始化智能体
        
        Args:
            knowledge_base: 知识库实例
        """
        self.knowledge_base = knowledge_base
        self.llm = DeepSeekLLM()
        self.memory = ConversationBufferWindowMemory(
            k=5,  # 保留最近5轮对话
            return_messages=True
        )
        self.qa_chain = None
        
        # 初始化QA链
        self._setup_qa_chain()
        
        logger.info("游戏攻略智能体初始化完成")
    
    def _setup_qa_chain(self):
        """设置问答链"""
        try:
            # 定义提示模板
            prompt_template = """你是一个专业的游戏攻略助手。请根据以下上下文信息回答用户的问题。

上下文信息：
{context}

历史对话：
{chat_history}

用户问题：{question}

请提供详细、准确、实用的游戏攻略建议。如果上下文中没有相关信息，请诚实说明并提供一般性的游戏建议。
回答要友好、专业，使用中文。

回答："""

            PROMPT = PromptTemplate(
                template=prompt_template,
                input_variables=["context", "question", "chat_history"]
            )
            
            # 创建检索QA链
            if self.knowledge_base.vector_store is not None:
                self.qa_chain = RetrievalQA.from_chain_type(
                    llm=self.llm,
                    chain_type="stuff",
                    retriever=self.knowledge_base.vector_store.as_retriever(
                        search_kwargs={"k": 5}
                    ),
                    chain_type_kwargs={"prompt": PROMPT},
                    return_source_documents=True
                )
                logger.info("QA链设置完成")
            else:
                logger.warning("向量数据库未初始化，无法创建QA链")
                
        except Exception as e:
            logger.error(f"设置QA链失败: {str(e)}")
            raise
    
    def chat(self, user_input: str) -> str:
        """
        与用户对话
        
        Args:
            user_input: 用户输入
            
        Returns:
            智能体回复
        """
        try:
            logger.info(f"用户输入: {user_input}")
            
            if self.qa_chain is None:
                # 如果没有QA链，直接使用LLM
                response = self._fallback_response(user_input)
            else:
                # 使用RAG流程
                response = self._rag_response(user_input)
            
            # 保存对话到内存
            self.memory.save_context(
                {"input": user_input},
                {"output": response}
            )
            
            logger.info(f"智能体回复: {response[:100]}...")
            return response
            
        except Exception as e:
            logger.error(f"对话处理失败: {str(e)}")
            return "抱歉，我遇到了一些技术问题，请稍后再试。"
    
    def _rag_response(self, user_input: str) -> str:
        """
        使用RAG流程生成回复
        
        Args:
            user_input: 用户输入
            
        Returns:
            生成的回复
        """
        try:
            # 获取历史对话
            chat_history = self._get_chat_history()
            
            # 使用QA链生成回复
            result = self.qa_chain({
                "query": user_input,
                "chat_history": chat_history
            })
            
            response = result["result"]
            source_docs = result.get("source_documents", [])
            
            # 记录使用的源文档
            if source_docs:
                logger.info(f"使用了 {len(source_docs)} 个相关文档")
            
            return response
            
        except Exception as e:
            logger.error(f"RAG回复生成失败: {str(e)}")
            return self._fallback_response(user_input)
    
    def _fallback_response(self, user_input: str) -> str:
        """
        备用回复方案（直接使用LLM）
        
        Args:
            user_input: 用户输入
            
        Returns:
            生成的回复
        """
        try:
            # 获取历史对话
            chat_history = self._get_chat_history()
            
            # 构建提示
            prompt = f"""你是一个专业的游戏攻略助手。

历史对话：
{chat_history}

用户问题：{user_input}

请提供友好、专业的游戏攻略建议，使用中文回答。

回答："""
            
            response = self.llm._call(prompt)
            return response
            
        except Exception as e:
            logger.error(f"备用回复生成失败: {str(e)}")
            return "抱歉，我现在无法处理您的问题，请稍后再试。"
    
    def _get_chat_history(self) -> str:
        """
        获取格式化的聊天历史
        
        Returns:
            格式化的聊天历史字符串
        """
        try:
            messages = self.memory.chat_memory.messages
            if not messages:
                return "无历史对话"
            
            history_lines = []
            for i in range(0, len(messages), 2):
                if i + 1 < len(messages):
                    user_msg = messages[i].content
                    ai_msg = messages[i + 1].content
                    history_lines.append(f"用户: {user_msg}")
                    history_lines.append(f"助手: {ai_msg}")
            
            return "\n".join(history_lines[-10:])  # 只保留最近5轮对话
            
        except Exception as e:
            logger.error(f"获取聊天历史失败: {str(e)}")
            return "无历史对话"
    
    def search_knowledge(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        搜索知识库
        
        Args:
            query: 搜索查询
            k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        try:
            if self.knowledge_base.vector_store is None:
                return []
            
            docs = self.knowledge_base.search_similar(query, k)
            
            results = []
            for doc in docs:
                results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata
                })
            
            return results
            
        except Exception as e:
            logger.error(f"知识库搜索失败: {str(e)}")
            return []
    
    def clear_memory(self):
        """清除对话记忆"""
        self.memory.clear()
        logger.info("对话记忆已清除")
