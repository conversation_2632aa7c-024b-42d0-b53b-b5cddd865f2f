// 金融智能助手前端脚本

class FinancialChat {
    constructor() {
        this.initElements();
        this.bindEvents();
        this.checkStatus();
    }

    initElements() {
        this.chatMessages = document.getElementById('chat-messages');
        this.userInput = document.getElementById('user-input');
        this.sendBtn = document.getElementById('send-btn');
        this.initBtn = document.getElementById('init-btn');
        this.clearBtn = document.getElementById('clear-btn');
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.kbStatus = document.getElementById('kb-status');
        this.agentStatus = document.getElementById('agent-status');
    }

    bindEvents() {
        // 发送按钮点击事件
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        
        // 输入框回车事件
        this.userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 初始化按钮点击事件
        this.initBtn.addEventListener('click', () => this.initializeSystem());
        
        // 清除对话按钮点击事件
        this.clearBtn.addEventListener('click', () => this.clearChat());
    }

    async checkStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            this.updateStatus('kb-status', data.knowledge_base_ready);
            this.updateStatus('agent-status', data.agent_ready);
            
            // 如果系统已就绪，启用输入
            if (data.knowledge_base_ready && data.agent_ready) {
                this.enableInput();
            }
        } catch (error) {
            console.error('检查状态失败:', error);
        }
    }

    updateStatus(elementId, isReady) {
        const element = document.getElementById(elementId);
        if (isReady) {
            element.classList.add('ready');
        } else {
            element.classList.remove('ready');
        }
    }

    async initializeSystem() {
        this.showLoading();
        this.initBtn.disabled = true;
        this.initBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 初始化中...';
        
        try {
            const response = await fetch('/api/init', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.addMessage('系统初始化完成！现在可以开始对话了。', 'bot');
                this.enableInput();
                this.checkStatus();
            } else {
                this.addMessage(`初始化失败: ${data.error}`, 'bot');
            }
        } catch (error) {
            console.error('初始化失败:', error);
            this.addMessage('初始化失败，请检查网络连接。', 'bot');
        } finally {
            this.hideLoading();
            this.initBtn.disabled = false;
            this.initBtn.innerHTML = '<i class="fas fa-rocket"></i> 初始化系统';
        }
    }

    async sendMessage() {
        const message = this.userInput.value.trim();
        if (!message) return;

        // 添加用户消息到界面
        this.addMessage(message, 'user');
        this.userInput.value = '';
        
        // 禁用输入
        this.disableInput();
        
        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message: message })
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.addMessage(data.response, 'bot');
            } else {
                this.addMessage(`错误: ${data.error}`, 'bot');
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addMessage('发送消息失败，请检查网络连接。', 'bot');
        } finally {
            this.enableInput();
        }
    }

    addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // 处理换行
        const formattedContent = content.replace(/\n/g, '<br>');
        messageContent.innerHTML = `<p>${formattedContent}</p>`;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    clearChat() {
        // 保留欢迎消息，清除其他消息
        const messages = this.chatMessages.querySelectorAll('.message');
        for (let i = 1; i < messages.length; i++) {
            messages[i].remove();
        }
        
        this.addMessage('对话已清除，可以开始新的对话了。', 'bot');
    }

    enableInput() {
        this.userInput.disabled = false;
        this.sendBtn.disabled = false;
        this.userInput.placeholder = '请输入您的金融问题...';
        this.userInput.focus();
    }

    disableInput() {
        this.userInput.disabled = true;
        this.sendBtn.disabled = true;
        this.userInput.placeholder = '正在处理中...';
    }

    showLoading() {
        this.loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new FinancialChat();
});
