"""
简化版金融智能助手
不依赖ModelScope，使用基础的DeepSeek API
"""

import os
from loguru import logger
from app.deepseek_client import DeepSeekLLM

class SimpleFinancialAgent:
    """简化版金融智能助手"""
    
    def __init__(self):
        """初始化智能体"""
        self.llm = DeepSeekLLM()
        self.chat_history = []
        logger.info("简化版金融智能助手初始化完成")
    
    def chat(self, user_input: str) -> str:
        """
        处理用户输入并返回回复
        
        Args:
            user_input: 用户输入的消息
            
        Returns:
            智能体的回复
        """
        try:
            # 构建上下文
            context = self._build_context()
            
            # 构建提示
            prompt = f"""你是一个专业的金融顾问助手。

历史对话：
{context}

用户问题：{user_input}

请提供友好、专业的金融建议和分析，使用中文回答。注意：本建议仅供参考，投资有风险，请谨慎决策。

回答："""

            # 调用LLM
            response = self.llm.generate(prompt)
            
            # 保存对话历史
            self.chat_history.append({
                'user': user_input,
                'assistant': response
            })
            
            # 限制历史记录长度
            if len(self.chat_history) > 5:
                self.chat_history = self.chat_history[-5:]
            
            return response
            
        except Exception as e:
            logger.error(f"对话处理错误: {str(e)}")
            return f"抱歉，处理您的问题时出现了错误：{str(e)}"
    
    def _build_context(self) -> str:
        """构建对话上下文"""
        if not self.chat_history:
            return "无历史对话"
        
        context_parts = []
        for i, chat in enumerate(self.chat_history[-3:], 1):  # 只取最近3轮对话
            context_parts.append(f"第{i}轮:")
            context_parts.append(f"用户: {chat['user']}")
            context_parts.append(f"助手: {chat['assistant']}")
            context_parts.append("")
        
        return "\n".join(context_parts)
    
    def clear_history(self):
        """清除对话历史"""
        self.chat_history = []
        logger.info("对话历史已清除")
