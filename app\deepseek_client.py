"""
DeepSeek API客户端
提供与DeepSeek API的集成功能
"""

import openai
from typing import List, Dict, Any
from loguru import logger
from config.settings import Config

class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self):
        """初始化DeepSeek客户端"""
        self.api_key = Config.DEEPSEEK_API_KEY
        self.base_url = Config.DEEPSEEK_BASE_URL
        
        if not self.api_key:
            raise ValueError("DeepSeek API密钥未设置")
        
        # 配置OpenAI客户端以使用DeepSeek API
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        logger.info("DeepSeek客户端初始化完成")
    
    def chat_completion(self, messages: List[Dict[str, str]], 
                       model: str = "deepseek-chat",
                       temperature: float = 0.7,
                       max_tokens: int = 2000) -> str:
        """
        调用DeepSeek聊天完成API
        
        Args:
            messages: 对话消息列表
            model: 使用的模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            生成的回复文本
        """
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {str(e)}")
            raise
    
    def generate_response(self, prompt: str, context: str = "") -> str:
        """
        生成回复
        
        Args:
            prompt: 用户输入
            context: 上下文信息
            
        Returns:
            生成的回复
        """
        system_message = """你是一个专业的金融顾问助手。你的任务是：
1. 根据提供的金融知识库内容回答用户问题
2. 提供准确、详细、实用的金融建议和分析
3. 如果知识库中没有相关信息，请诚实说明并提供一般性金融知识
4. 回答要友好、专业，使用中文
5. 重要提醒：所有建议仅供参考，投资有风险，请用户谨慎决策

请基于以下上下文信息回答用户问题：
{context}
"""
        
        messages = [
            {"role": "system", "content": system_message.format(context=context)},
            {"role": "user", "content": prompt}
        ]
        
        return self.chat_completion(messages)
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            test_messages = [
                {"role": "user", "content": "你好"}
            ]
            
            response = self.chat_completion(test_messages, max_tokens=10)
            logger.info("DeepSeek API连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"DeepSeek API连接测试失败: {str(e)}")
            return False
