#!/usr/bin/env python3
"""
金融智能助手简化启动脚本
"""

import os
import sys
from loguru import logger

def main():
    """主函数"""
    logger.info("💰 金融智能助手启动中...")
    
    # 检查基础依赖
    try:
        import flask
        logger.info("Flask检查通过")
    except ImportError as e:
        logger.error(f"缺少Flask: {e}")
        sys.exit(1)
    
    # 创建目录
    directories = ['data', 'logs', 'data/vector_db']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"目录已创建: {directory}")
    
    # 启动简化版Flask应用
    try:
        from flask import Flask, render_template, jsonify
        
        app = Flask(__name__)
        
        @app.route('/')
        def index():
            return render_template('index.html')
        
        @app.route('/api/status')
        def status():
            return jsonify({
                'status': 'running',
                'message': '金融智能助手运行中',
                'dependencies_status': 'partial'
            })
        
        logger.info("Flask应用启动成功")
        logger.info("访问地址: http://localhost:5000")
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
