"""
游戏攻略智能助手 - 主应用入口
基于Flask + LangChain + DeepSeek API + ModelScope数据集
"""

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import os
import logging
from loguru import logger

# 加载环境变量
load_dotenv()

# 导入自定义模块
from app.agent import GameGuideAgent
from app.knowledge_base import KnowledgeBase
from config.settings import Config

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 启用CORS
    CORS(app)
    
    # 配置日志
    logger.add("logs/app.log", rotation="1 day", retention="7 days")
    
    return app

app = create_app()

# 初始化智能体和知识库
knowledge_base = None
game_agent = None

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """聊天接口"""
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        
        if not user_message:
            return jsonify({'error': '消息不能为空'}), 400
        
        # 使用智能体处理消息
        if game_agent is None:
            return jsonify({'error': '智能体未初始化'}), 500
            
        response = game_agent.chat(user_message)
        
        return jsonify({
            'response': response,
            'status': 'success'
        })
        
    except Exception as e:
        logger.error(f"聊天接口错误: {str(e)}")
        return jsonify({'error': '服务器内部错误'}), 500

@app.route('/api/init', methods=['POST'])
def initialize():
    """初始化知识库和智能体"""
    global knowledge_base, game_agent
    
    try:
        logger.info("开始初始化知识库和智能体...")
        
        # 初始化知识库
        knowledge_base = KnowledgeBase()
        knowledge_base.load_data()
        
        # 初始化智能体
        game_agent = GameGuideAgent(knowledge_base)
        
        logger.info("初始化完成！")
        return jsonify({'status': 'success', 'message': '初始化完成'})
        
    except Exception as e:
        logger.error(f"初始化错误: {str(e)}")
        return jsonify({'error': f'初始化失败: {str(e)}'}), 500

@app.route('/api/status')
def status():
    """获取系统状态"""
    return jsonify({
        'knowledge_base_ready': knowledge_base is not None,
        'agent_ready': game_agent is not None,
        'status': 'running'
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
