"""
配置文件
"""
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """基础配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # DeepSeek API配置
    DEEPSEEK_API_KEY = os.environ.get('DEEPSEEK_API_KEY')
    DEEPSEEK_BASE_URL = os.environ.get('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
    
    # 数据路径配置
    DATA_PATH = os.environ.get('DATA_PATH', './data')
    VECTOR_DB_PATH = os.environ.get('VECTOR_DB_PATH', './data/vector_db')
    
    # 模型配置
    EMBEDDING_MODEL = os.environ.get('EMBEDDING_MODEL', 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2')
    CHUNK_SIZE = int(os.environ.get('CHUNK_SIZE', 1000))
    CHUNK_OVERLAP = int(os.environ.get('CHUNK_OVERLAP', 200))
    
    # 确保数据目录存在
    @staticmethod
    def init_app(app):
        os.makedirs(Config.DATA_PATH, exist_ok=True)
        os.makedirs(Config.VECTOR_DB_PATH, exist_ok=True)
