#!/usr/bin/env python3
"""
游戏攻略智能助手启动脚本
"""

import os
import sys
from loguru import logger

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        import langchain
        import openai
        import modelscope
        import chromadb
        import sentence_transformers
        logger.info("所有依赖检查通过")
        return True
    except ImportError as e:
        logger.error(f"缺少依赖: {e}")
        logger.info("请运行: pip install -r requirements.txt")
        return False

def setup_directories():
    """创建必要的目录"""
    directories = ['data', 'logs', 'data/vector_db']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"目录已创建: {directory}")

def main():
    """主函数"""
    logger.info("🎮 游戏攻略智能助手启动中...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    setup_directories()
    
    # 启动Flask应用
    try:
        from app import app
        logger.info("Flask应用启动成功")
        logger.info("访问地址: http://localhost:5000")
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
