"""
知识库管理模块
负责从ModelScope下载数据集，处理文本，构建向量数据库
"""

import os
import pandas as pd
from typing import List, Dict, Any
from loguru import logger
from modelscope.msdatasets import MsDataset
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.vectorstores import Chroma
from langchain.docstore.document import Document
from config.settings import Config

class KnowledgeBase:
    """知识库管理类"""
    
    def __init__(self):
        """初始化知识库"""
        self.config = Config()
        self.embeddings = None
        self.vector_store = None
        self.text_splitter = None
        
        # 初始化文本分割器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.CHUNK_SIZE,
            chunk_overlap=self.config.CHUNK_OVERLAP,
            separators=["\n\n", "\n", "。", "！", "？", "；", " ", ""]
        )
        
        logger.info("知识库管理器初始化完成")
    
    def _init_embeddings(self):
        """初始化嵌入模型"""
        if self.embeddings is None:
            logger.info("正在初始化嵌入模型...")
            self.embeddings = HuggingFaceEmbeddings(
                model_name=self.config.EMBEDDING_MODEL,
                model_kwargs={'device': 'cpu'},
                encode_kwargs={'normalize_embeddings': True}
            )
            logger.info("嵌入模型初始化完成")
    
    def download_dataset(self, dataset_name: str = 'peopletech/Financial') -> pd.DataFrame:
        """
        从ModelScope下载数据集
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            下载的数据集DataFrame
        """
        try:
            logger.info(f"正在下载数据集: {dataset_name}")
            
            # 下载数据集
            ds = MsDataset.load(dataset_name)
            
            # 转换为DataFrame
            data_list = []
            for item in ds:
                data_list.append(item)
            
            df = pd.DataFrame(data_list)
            logger.info(f"数据集下载完成，共 {len(df)} 条记录")
            
            # 保存到本地
            data_path = os.path.join(self.config.DATA_PATH, 'dataset.csv')
            df.to_csv(data_path, index=False, encoding='utf-8')
            logger.info(f"数据集已保存到: {data_path}")
            
            return df
            
        except Exception as e:
            logger.error(f"下载数据集失败: {str(e)}")
            raise
    
    def process_documents(self, df: pd.DataFrame) -> List[Document]:
        """
        处理文档数据，转换为LangChain Document格式
        
        Args:
            df: 数据集DataFrame
            
        Returns:
            Document列表
        """
        documents = []
        
        try:
            logger.info("正在处理文档数据...")
            
            for idx, row in df.iterrows():
                # 根据数据集结构调整字段名
                # 这里假设有text字段，你可能需要根据实际数据集调整
                text_content = ""
                
                # 尝试不同的可能字段名
                possible_text_fields = ['text', 'content', 'question', 'answer', 'description']
                for field in possible_text_fields:
                    if field in row and pd.notna(row[field]):
                        text_content += str(row[field]) + " "
                
                if text_content.strip():
                    # 创建Document对象
                    doc = Document(
                        page_content=text_content.strip(),
                        metadata={
                            'source': f'dataset_row_{idx}',
                            'row_id': idx
                        }
                    )
                    documents.append(doc)
            
            logger.info(f"文档处理完成，共 {len(documents)} 个文档")
            return documents
            
        except Exception as e:
            logger.error(f"处理文档失败: {str(e)}")
            raise
    
    def create_vector_store(self, documents: List[Document]):
        """
        创建向量数据库
        
        Args:
            documents: 文档列表
        """
        try:
            logger.info("正在创建向量数据库...")
            
            # 初始化嵌入模型
            self._init_embeddings()
            
            # 分割文档
            split_docs = self.text_splitter.split_documents(documents)
            logger.info(f"文档分割完成，共 {len(split_docs)} 个文档块")
            
            # 创建向量存储
            self.vector_store = Chroma.from_documents(
                documents=split_docs,
                embedding=self.embeddings,
                persist_directory=self.config.VECTOR_DB_PATH
            )
            
            # 持久化
            self.vector_store.persist()
            logger.info("向量数据库创建完成")
            
        except Exception as e:
            logger.error(f"创建向量数据库失败: {str(e)}")
            raise
    
    def load_existing_vector_store(self):
        """加载已存在的向量数据库"""
        try:
            if os.path.exists(self.config.VECTOR_DB_PATH):
                logger.info("正在加载已存在的向量数据库...")
                
                # 初始化嵌入模型
                self._init_embeddings()
                
                # 加载向量存储
                self.vector_store = Chroma(
                    persist_directory=self.config.VECTOR_DB_PATH,
                    embedding_function=self.embeddings
                )
                
                logger.info("向量数据库加载完成")
                return True
            else:
                logger.info("未找到已存在的向量数据库")
                return False
                
        except Exception as e:
            logger.error(f"加载向量数据库失败: {str(e)}")
            return False
    
    def search_similar(self, query: str, k: int = 5) -> List[Document]:
        """
        搜索相似文档
        
        Args:
            query: 查询文本
            k: 返回结果数量
            
        Returns:
            相似文档列表
        """
        if self.vector_store is None:
            raise ValueError("向量数据库未初始化")
        
        try:
            results = self.vector_store.similarity_search(query, k=k)
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            raise
    
    def load_data(self):
        """加载数据（优先加载已存在的，否则重新下载）"""
        try:
            # 尝试加载已存在的向量数据库
            if self.load_existing_vector_store():
                logger.info("使用已存在的向量数据库")
                return
            
            # 如果没有已存在的数据库，则重新创建
            logger.info("重新创建向量数据库...")
            
            # 下载数据集
            df = self.download_dataset()
            
            # 处理文档
            documents = self.process_documents(df)
            
            # 创建向量数据库
            self.create_vector_store(documents)
            
            logger.info("知识库加载完成")
            
        except Exception as e:
            logger.error(f"加载知识库失败: {str(e)}")
            raise
