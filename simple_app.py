"""
简化版金融智能助手 - 主应用入口
不依赖复杂的知识库，直接使用DeepSeek API
"""

import os
from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
from loguru import logger
from app.simple_agent import SimpleFinancialAgent
from config.settings import Config

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 配置日志
logger.add("logs/app.log", rotation="1 day", retention="7 days")

# 初始化智能体
financial_agent = None

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天消息"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': '消息不能为空'}), 400
        
        # 使用智能体处理消息
        if financial_agent is None:
            return jsonify({'error': '智能体未初始化'}), 500
            
        response = financial_agent.chat(user_message)
        
        return jsonify({
            'response': response,
            'status': 'success'
        })
        
    except Exception as e:
        logger.error(f"聊天处理错误: {str(e)}")
        return jsonify({'error': f'处理消息时出错: {str(e)}'}), 500

@app.route('/api/init', methods=['POST'])
def initialize():
    """初始化智能体"""
    global financial_agent
    
    try:
        logger.info("开始初始化智能体...")
        
        # 初始化智能体
        financial_agent = SimpleFinancialAgent()
        
        logger.info("初始化完成！")
        return jsonify({'status': 'success', 'message': '初始化完成'})
        
    except Exception as e:
        logger.error(f"初始化错误: {str(e)}")
        return jsonify({'error': f'初始化失败: {str(e)}'}), 500

@app.route('/api/clear', methods=['POST'])
def clear_chat():
    """清除对话历史"""
    try:
        if financial_agent:
            financial_agent.clear_history()
        return jsonify({'status': 'success', 'message': '对话历史已清除'})
    except Exception as e:
        logger.error(f"清除历史错误: {str(e)}")
        return jsonify({'error': f'清除失败: {str(e)}'}), 500

@app.route('/api/status')
def status():
    """获取系统状态"""
    return jsonify({
        'agent_ready': financial_agent is not None,
        'status': 'running',
        'mode': 'simplified'
    })

if __name__ == '__main__':
    # 创建必要目录
    os.makedirs('logs', exist_ok=True)
    
    logger.info("💰 简化版金融智能助手启动中...")
    app.run(debug=True, host='0.0.0.0', port=5000)
