"""
基础功能测试
"""

import unittest
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.deepseek_client import DeepSeekClient
from config.settings import Config

class TestBasicFunctionality(unittest.TestCase):
    """基础功能测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.config = Config()
    
    def test_config_loading(self):
        """测试配置加载"""
        self.assertIsNotNone(self.config.DEEPSEEK_API_KEY)
        self.assertIsNotNone(self.config.DEEPSEEK_BASE_URL)
        self.assertTrue(self.config.CHUNK_SIZE > 0)
    
    def test_deepseek_client_init(self):
        """测试DeepSeek客户端初始化"""
        try:
            client = DeepSeekClient()
            self.assertIsNotNone(client.client)
            self.assertEqual(client.api_key, self.config.DEEPSEEK_API_KEY)
        except Exception as e:
            self.fail(f"DeepSeek客户端初始化失败: {e}")
    
    def test_deepseek_connection(self):
        """测试DeepSeek API连接"""
        try:
            client = DeepSeekClient()
            result = client.test_connection()
            self.assertTrue(result, "DeepSeek API连接测试失败")
        except Exception as e:
            self.skipTest(f"跳过API连接测试: {e}")
    
    def test_directories_creation(self):
        """测试目录创建"""
        from config.settings import Config
        Config.init_app(None)
        
        self.assertTrue(os.path.exists(Config.DATA_PATH))
        self.assertTrue(os.path.exists(Config.VECTOR_DB_PATH))

if __name__ == '__main__':
    unittest.main()
