# 🎮 游戏攻略智能助手

基于Flask + LangChain + DeepSeek API + ModelScope数据集的智能游戏攻略助手。

## ✨ 功能特性

- 🤖 **智能对话**: 基于DeepSeek API的自然语言处理
- 📚 **知识库**: 使用ModelScope数据集构建专业知识库
- 🔍 **RAG检索**: 检索增强生成，提供准确的攻略建议
- 💬 **多轮对话**: 支持上下文记忆的连续对话
- 🌐 **Web界面**: 美观易用的聊天界面
- ⚡ **实时响应**: 快速的问答响应

## 🛠️ 技术栈

- **后端**: Flask + LangChain
- **AI模型**: DeepSeek API
- **知识库**: ModelScope数据集
- **向量数据库**: ChromaDB
- **嵌入模型**: Sentence Transformers
- **前端**: HTML + CSS + JavaScript + Bootstrap

## 📦 安装部署

### 1. 克隆项目
```bash
git clone <项目地址>
cd final
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境变量
编辑 `.env` 文件，确保DeepSeek API密钥正确：
```env
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_BASE_URL=https://api.deepseek.com
```

### 4. 启动应用
```bash
python run.py
```

### 5. 访问应用
打开浏览器访问: http://localhost:5000

## 🚀 使用说明

1. **初始化系统**: 首次使用时点击"初始化系统"按钮
2. **开始对话**: 系统初始化完成后即可开始提问
3. **游戏攻略**: 询问各种游戏相关问题
4. **清除对话**: 可随时清除对话历史重新开始

## 📁 项目结构

```
final/
├── app/                    # 应用核心模块
│   ├── __init__.py
│   ├── agent.py           # 智能体核心逻辑
│   ├── deepseek_client.py # DeepSeek API客户端
│   ├── knowledge_base.py  # 知识库管理
│   └── langchain_integration.py # LangChain集成
├── config/                # 配置文件
│   └── settings.py
├── templates/             # HTML模板
│   └── index.html
├── static/               # 静态资源
│   ├── style.css
│   └── script.js
├── tests/                # 测试文件
│   └── test_basic.py
├── data/                 # 数据存储目录
├── logs/                 # 日志目录
├── app.py               # Flask应用入口
├── run.py               # 启动脚本
├── requirements.txt     # 依赖列表
├── .env                 # 环境变量
└── README.md           # 项目说明
```

## 🧪 运行测试

```bash
python -m pytest tests/ -v
```

或者运行单个测试文件：
```bash
python tests/test_basic.py
```

## 🔧 配置说明

### 环境变量配置
- `DEEPSEEK_API_KEY`: DeepSeek API密钥
- `DEEPSEEK_BASE_URL`: DeepSeek API基础URL
- `DATA_PATH`: 数据存储路径
- `VECTOR_DB_PATH`: 向量数据库路径
- `EMBEDDING_MODEL`: 嵌入模型名称
- `CHUNK_SIZE`: 文本分块大小
- `CHUNK_OVERLAP`: 文本分块重叠大小

### 数据集配置
默认使用 `peopletech/Financial` 数据集，可在 `app/knowledge_base.py` 中修改为其他游戏相关数据集。

## 🐛 故障排除

### 常见问题

1. **依赖安装失败**
   - 确保Python版本 >= 3.8
   - 使用虚拟环境: `python -m venv venv`

2. **API连接失败**
   - 检查DeepSeek API密钥是否正确
   - 确认网络连接正常

3. **知识库初始化失败**
   - 检查ModelScope数据集访问权限
   - 确保有足够的磁盘空间

4. **向量数据库错误**
   - 删除 `data/vector_db` 目录重新初始化
   - 检查ChromaDB版本兼容性

## 📝 开发说明

### 添加新功能
1. 在 `app/` 目录下创建新模块
2. 在 `app.py` 中添加新的API路由
3. 更新前端界面（如需要）
4. 编写相应的测试用例

### 自定义数据集
1. 修改 `app/knowledge_base.py` 中的 `download_dataset` 方法
2. 调整 `process_documents` 方法以适配新数据格式
3. 重新初始化知识库

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系方式

如有问题请联系开发者。
